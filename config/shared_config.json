{"__title_Bjorn__": "Settings", "manual_mode": false, "websrv": true, "web_increment ": false, "debug_mode": true, "scan_vuln_running": false, "retry_success_actions": false, "retry_failed_actions": true, "blacklistcheck": true, "displaying_csv": true, "log_debug": true, "log_info": true, "log_warning": true, "log_error": true, "log_critical": true, "startup_delay": 10, "web_delay": 2, "screen_delay": 1, "comment_delaymin": 15, "comment_delaymax": 30, "livestatus_delay": 8, "image_display_delaymin": 2, "image_display_delaymax": 8, "scan_interval": 180, "scan_vuln_interval": 900, "failed_retry_delay": 600, "success_retry_delay": 900, "ref_width": 122, "ref_height": 250, "epd_type": "epd2in13_V4", "__title_lists__": "List Settings", "portlist": [20, 21, 22, 23, 25, 53, 69, 80, 110, 111, 135, 137, 139, 143, 161, 162, 389, 443, 445, 512, 513, 514, 587, 636, 993, 995, 1080, 1433, 1521, 2049, 3306, 3389, 5000, 5001, 5432, 5900, 8080, 8443, 9090, 10000], "mac_scan_blacklist": ["00:11:32:c4:71:9b", "00:11:32:c4:71:9a"], "ip_scan_blacklist": ["***********", "***********2", "************", "************", "************", "************"], "steal_file_names": ["ssh.csv", "hack.txt"], "steal_file_extensions": [".bjorn", ".hack", ".flag"], "__title_network__": "Network", "nmap_scan_aggressivity": "-T2", "portstart": 1, "portend": 2, "__title_timewaits__": "Time Wait Settings", "timewait_smb": 0, "timewait_ssh": 0, "timewait_telnet": 0, "timewait_ftp": 0, "timewait_sql": 0, "timewait_rdp": 0}